const requestlib = require("../../lib/request");
const HubLogger = require("../../log");
const constants = require("../../constants");
const { LH_ERROR_MESSAGES, LH_SUPPORTED_BROWSERS, browserVersionStrToFloat, LH_MIN_CHROME, LH_FAIL_SESSION_ARG, THROTTLING_PROFILE, LH_DEFAULT_CONFIG, LH_PERFORMANCE_ERROR_MESSAGES } = constants;
const customExecutorHelper = require("./../customSeleniumHandling/customExecutorHelper");
const { isUndefined, isEmptyJson, isString, isFalseString, isTrueString, isNotUndefined } = require("../../typeSanity");
const { timeout, endpoint, buffer_timeout, jsonListEndpoint } = constants.LIGHTHOUSE_AUTOMATE;
const { REMOTE_DEBUGGER_PORT, PLAYWRIGHT_LH_PORT, PLAYWRIGHT, SELENIUM, PUPPETEER } = require("../../config/socketConstants");
const pubSub = require("../../pubSub");
const { getOpenUrl } = require("../../services/selenium");
const { setNestedValue, isHash, isDefined, getDate } = require("../../helper");
const RestAPIHandler = require('../../helpers/customSeleniumHandling/restAPIHandler');
const Qig = require('../qig');
const AWS = require('aws-sdk');

const REPORT = "report", ASSERT = "assert";
const LH_VALIDATED_MSG = "validated";
const TAG = "lighthouse";

const getProduct = (keyObject) => {
  if (keyObject.isPlaywright) {
    return PLAYWRIGHT;
  } else if (keyObject.isPuppeteer) {
    return PUPPETEER;
  } else if (keyObject.selenium_version) {
    return !keyObject.realMobile ? SELENIUM : false;
  }
  return false;
};

const lighthouseExecutorValidation = (keyObject, body) => {
  if (!keyObject.lighthouseAutomate.assert_limit || keyObject.lighthouseAutomate.report_limit) {
    return LH_ERROR_MESSAGES["js_executor_assert_limit_not_found"];
  }

  if (!getProduct(keyObject)) {
    return LH_ERROR_MESSAGES["js_executor_framework_not_supported"];
  }

  if (!LH_SUPPORTED_BROWSERS.includes(keyObject.browser)) {
    return LH_ERROR_MESSAGES["browser_not_supported"];
  } else if (browserVersionStrToFloat(keyObject.browser_version) < LH_MIN_CHROME) {
    return LH_ERROR_MESSAGES["chrome_version_not_supported"];
  }

  if (!body) return LH_VALIDATED_MSG;

  if (body.lhConfig && !isHash(body.lhConfig)) {
    return LH_ERROR_MESSAGES["js_executor_invalid_lh_config"];
  }

  if (body.throttling) {
    const throttling = body.throttling;
    const profile = THROTTLING_PROFILE[throttling];
    if (!profile) {
      return LH_ERROR_MESSAGES["js_executor_invalid_throttling"];
    }
  }

  if (body.assertResult && body.assertResult.categories) {
    const categories = body.assertResult.categories;
    if (!isHash(categories)) {
      return LH_ERROR_MESSAGES["js_executor_invalid_categories_format"];
    }

    const LH_SUPPORTED_CATEGORIES = ["performance", "accessibility", "best-practices", "seo", "pwa"];
    for (const [key, value] of Object.entries(categories)) {
      if (!LH_SUPPORTED_CATEGORIES.includes(key)) {
        return LH_ERROR_MESSAGES["js_executor_invalid_categories"];
      }
      if (!isUndefined(value) && (isNaN(parseInt(value)) || parseInt(value) < 0 || parseInt(value) > 100)) {
        return LH_ERROR_MESSAGES["js_executor_invalid_categories_value"];
      }
    }
  }

  if (body.assertResult && body.assertResult.metrics) {
    const metrics = body.assertResult.metrics;
    if (!isHash(metrics)) {
      return LH_ERROR_MESSAGES["js_executor_invalid_metrics_format"];
    }

    for (const [_, value] of Object.entries(metrics)) {
      const moreThan = value.moreThan;
      const lessThan = value.lessThan;
      const metricUnit = value.metricUnit;

      if ((isUndefined(moreThan) && isUndefined(lessThan)) || isUndefined(metricUnit)) {
        return LH_ERROR_MESSAGES["js_executor_invalid_metrics_format"];
      }

      if(isDefined(moreThan) && isNaN(parseFloat(moreThan))) {
        return LH_ERROR_MESSAGES["js_executor_invalid_metrics_value"];
      }

      if(isDefined(lessThan) && isNaN(parseFloat(lessThan))) {
        return LH_ERROR_MESSAGES["js_executor_invalid_metrics_value"];
      }

      if(isDefined(moreThan) && isDefined(lessThan)) {
        return LH_ERROR_MESSAGES["js_executor_invalid_metrics_value"];
      }
    }
  }
  return LH_VALIDATED_MSG;
};

const fixCustomBrowserstackArgs = (body) => {
  if (!body) return;

  // fix throttling
  if (body.throttling) {
    if (isUndefined(body.lhConfig)) { // Add default config if no config is passed
      body.lhConfig = LH_DEFAULT_CONFIG;
    }
    const profile = THROTTLING_PROFILE[body.throttling];
    setNestedValue(body, "lhConfig.settings.throttling", profile);
    delete body.throttling;
  }

  // fix categories
  if (body.assertResult && body.assertResult.categories) {
    const categories = body.assertResult.categories;
    for (const [key, value] of Object.entries(categories)) {
      if (isUndefined(value) || isNaN(parseInt(value))) {
        body.assertResult.categories[key] = 50;
      } else {
        body.assertResult.categories[key] = parseInt(value);
      }
    }
  }
}

const processLighthouseAssertResult = (assertResult, lhResponseData) => {
  const categories = assertResult.categories;
  const metrics = assertResult.metrics;
  const data = JSON.parse(lhResponseData);
  const result = {};
  result.url = data.finalUrl;
  result.failTest = false; // Fail test if any assertion fails and user passes the "sessionFail" param

  if (categories) {
    result.categories = {};
    for (const [key, value] of Object.entries(categories)) {
      if (!data.categories[key]) {
        result.categories[key] = "failed";
        result.failTest = true;
        continue;
      }
      const score = data.categories[key].score * 100;
      if (score >= value) {
        result.categories[key] = "passed";
      } else {
        result.categories[key] = "failed";
        result.failTest = true;
      }
    }
  }

  if (metrics) {
    result.metrics = {};
    for (const [key, value] of Object.entries(metrics)) {
      let metricUnit = value.metricUnit;
      let multiplier = 1;
      if (metricUnit == "score") {
        multiplier = 100;
      } else {
        metricUnit = "numericValue";
      }

      if (!data.audits[key]) {
        result.metrics[key] = "failed";
        result.failTest = true;
        continue;
      }

      const auditValue = parseFloat(data.audits[key][metricUnit]) * multiplier;
      if (isDefined(value.moreThan) &&  auditValue > parseFloat(value.moreThan) ) {
        result.metrics[key] = "passed";
      } else if (isDefined(value.lessThan) && auditValue < parseFloat(value.lessThan)) {
        result.metrics[key] = "passed";
      } else {
        result.metrics[key] = "failed";
        result.failTest = true;
      }
    }
  }

  return result;
}

const sendResponse = (keyObject, requestStateObj, parsedCommand, response, executorStatus = "", errorString = "") => {
  requestStateObj.hash = "GET:value";
  requestStateObj.data = JSON.stringify({
    sessionId: requestStateObj.clientSessionID,
    value: response,
  });
  customExecutorHelper.instrumentExecutorStatusAndSendResponse(
    executorStatus,
    errorString,
    parsedCommand.action,
    keyObject,
    requestStateObj,
    false
  );
};

const lighthouseExecutorHandler = (keyObject, requestStateObj, parsedCommand) => {
  const validationMessage = lighthouseExecutorValidation(keyObject, parsedCommand.arguments);
  fixCustomBrowserstackArgs(parsedCommand.arguments);
  if (validationMessage != LH_VALIDATED_MSG) {
    const response = { status: "error", value: validationMessage };
    sendResponse(keyObject, requestStateObj, parsedCommand, response);
    return;
  }
  parsedCommand.arguments = parsedCommand.arguments || {};
  lighthouseExecutor(keyObject, requestStateObj, parsedCommand);
};

const lighthouseExecutor = async (keyObject, requestStateObj, parsedCommand) => {
  let url = parsedCommand.arguments.url;
  const product = getProduct(keyObject);
  if (!url) {
    try {
      if (product == PLAYWRIGHT || product == PUPPETEER) {
        url = await getCurrentOpenUrlOnChromium(product, keyObject);
      } else {
        const getUrlResponse = await getOpenUrl(keyObject);
        const getUrlResponseData = JSON.parse(getUrlResponse.data);
        url = getUrlResponseData.value;
      }
    } catch (error) {
      const response = { status: "error", value: LH_ERROR_MESSAGES["js_executor_url_not_found"], performanceLogs: true };
      sendResponse(keyObject, requestStateObj, parsedCommand, response, 'error', LH_PERFORMANCE_ERROR_MESSAGES.JS_EXECUTOR_URL_NOT_FOUND);
      return;
    }
  }

  const lhResponse = await triggerLighthouse(url, keyObject, product, params = { type: ASSERT, body: parsedCommand.arguments });
  let lhResponseData;
  try {
    lhResponseData = JSON.parse(lhResponse);
  } catch (error) {
    const response = { status: "error", value: { url: url, data: lhResponse }, performanceLogs: true };
    HubLogger.miscLogger(TAG, `Error parsing response data: ${error.message} :: lhResponse: ${lhResponse}`, constants.LOG_LEVEL.ERROR);
    sendResponse(keyObject, requestStateObj, parsedCommand, response, 'error', LH_PERFORMANCE_ERROR_MESSAGES.ERROR_IN_RUN_LIGHTHOUSE);
    return;
  }
  if (!isTrueString(lhResponseData.lhSuccess)) {
    const response = { status: "error", value: { url: url, data: lhResponseData.data } , performanceLogs: true };
    sendResponse(keyObject, requestStateObj, parsedCommand, response, 'error', LH_PERFORMANCE_ERROR_MESSAGES.LIGHTHOUSE_ERROR);
    return;
  }

  const response = { status: "passed", value: { url: url }, performanceLogs: true };
  const jsonOutput = parsedCommand.arguments.executorOutput;
  if (isString(jsonOutput) && jsonOutput == "json") {
    response.report = lhResponseData.data;
    response.truncatePerformanceJson = true;
  } else {
    response.report = "Put executorOutput as json in arguments to get json report of the audit.";
  }

  if (parsedCommand.arguments.assertResult && (parsedCommand.arguments.assertResult.categories || parsedCommand.arguments.assertResult.metrics)) {
    try {
      const assertResult = processLighthouseAssertResult(parsedCommand.arguments.assertResult, lhResponseData.data);
      if(isTrueString(parsedCommand.arguments.assertResult.sessionFail) && isTrueString(assertResult.failTest)) {
        await markSessionFailed(keyObject);
      }
      response.status = (isTrueString(assertResult.failTest)) ? "failed" : "passed";
      delete assertResult.failTest;
      response.value = assertResult;
    } catch (error) {
      response.status = "error";
      response.value = "Failure in test assertion.";
      HubLogger.miscLogger(TAG, `Error performing assertion: ${error.message} :: lhResponse: ${lhResponse}`, constants.LOG_LEVEL.ERROR);
    }
    delete parsedCommand.arguments.executorOutput;
  }
  if (response.status === "error") {
    sendResponse(keyObject, requestStateObj, parsedCommand, response, 'error', LH_PERFORMANCE_ERROR_MESSAGES.ASSERTION_ERROR);
  } else {
    sendResponse(keyObject, requestStateObj, parsedCommand, response, 'success');
  }
};

const requestToTerminal = async (keyObject, opts, product) => {
  const { url, file_name, devtoolsPort, params } = opts;
  const { type, body = {} } = params;

  if (isUndefined(body.lhConfig) || isEmptyJson(body.lhConfig)) {
    body.lhConfig = JSON.stringify(LH_DEFAULT_CONFIG);
  } else {
    body.lhConfig = JSON.stringify(body.lhConfig);
  }

  const port = keyObject["os"].indexOf("win") > -1 ? "4567" : "45671";
  const lhOptions = {
    url: url,
    timeout: timeout,
    devtools_port: devtoolsPort,
    lhFlags: JSON.stringify({
      port: devtoolsPort,
      logLevel: "info",
      output: "json",
      // onlyCategories: body.onlyCategories,
    }),
    lhConfig: body.lhConfig,
    logHost: constants.zombie_server,
    clsLogHost: constants.cls_host,
    bucket: keyObject.logs_aws_bucket,
    folder: keyObject.rails_session_id,
    file: file_name,
    aws_secret: keyObject.logs_aws_secret,
    aws_key: keyObject.logs_aws_keys,
    aws_region: keyObject.logs_aws_region,
    aws_bucket: keyObject.logs_aws_bucket,
    automate_session_id: keyObject.rails_session_id,
    async: type == REPORT,
    edsKey: constants.eds_key,
    edsHost: constants.eds_server,
    edsPort: constants.eds_port,
    framework: product
  };

  const lhQueryParams = requestlib.getEncodedURLParams(lhOptions);
  const getOptions = {
    hostname: keyObject.rproxyHost,
    port: port,
    path: endpoint + "?" + lhQueryParams,
    agent: false,
    headers: {
      Connection: "keep-alive",
    },
    timeout: timeout + buffer_timeout,
  };
  requestlib.appendBStackHostHeader(keyObject.name, getOptions.headers);
  const result = await requestlib.call(getOptions);

  if (result.statusCode != 200 || isUndefined(result.data)) {
    throw new Error(
      `Exception raised in terminal: ${JSON.stringify(result.data)}`
    );
  }
  return result.data;
};

const triggerLighthouse = async (url, keyObject, product, params = { type: REPORT }) => {
  const limit_key = params.type == REPORT ? "report_limit" : "assert_limit";
  const lighthouseAutomate = keyObject.lighthouseAutomate;
  const limit = parseInt(lighthouseAutomate[limit_key]);
  const report_count = (lighthouseAutomate.report_counter || 0) + 1;
  const file_name = "lighthouse-report-" + report_count;
  const devtoolsPort = getValidDevtoolsPort(product, keyObject);
  let res;
  if (url && report_count <= limit) {
    lighthouseAutomate.report_counter = report_count;
    lighthouseAutomate.last_url = url;
    lighthouseAutomate.finalList = lighthouseAutomate.finalList || [];
    const opts = { url, file_name, devtoolsPort, params };
    lighthouseAutomate.finalList.push({timestamp: getDate(), url, report_count});
    try {
      res = await requestToTerminal(keyObject, opts, product);
      keyObject.lighthouseAutomate = lighthouseAutomate;
    } catch (error) {
      HubLogger.miscLogger(TAG, `lh${product} - session: ${keyObject.sessionId} - url: ${url} - error: ${error}`, constants.LOG_LEVEL.ERROR);
      res = LH_ERROR_MESSAGES["lh_error"];
    }
  } else {
    if (isUndefined(url)) {
      res = LH_ERROR_MESSAGES["undefined_test_url"];
    } else if (limit && report_count > limit) {
      lighthouseAutomate.limit_reached = true; // Used in logger to determine if report limit reached
      lighthouseAutomate.last_url = url;
      res = LH_ERROR_MESSAGES["report_limit_exhausted"] + limit.toString();
    } else {
      res = LH_ERROR_MESSAGES["lh_error"];
    }
  }
  // Update lh obj via pubsub
  const changes = {
    session: keyObject.rails_session_id,
    changed: {
      lighthouseAutomate: keyObject.lighthouseAutomate,
    },
  };
  pubSub.publish(constants.updateKeyObject, changes);
  HubLogger.miscLogger(`lighthouse-${product}-trigger-end`, `session: ${keyObject.rails_session_id} url: ${url}`, constants.LOG_LEVEL.INFO);
  return res;
}

const getPerformanceLogData = (keyObject) => {
  if (!keyObject.lighthouseAutomate.finalList) {
    return '';
  }
  const result = {};
  result.url = keyObject.lighthouseAutomate.last_url;
  if (keyObject.lighthouseAutomate.limit_reached) {
    return JSON.stringify(result);
  }
  const finalList = keyObject.lighthouseAutomate.finalList;
  const lastObj = finalList[finalList.length - 1];
  const report_count = lastObj.report_count;
  const { rails_session_id } = keyObject;

  const credentials = new AWS.Credentials({
    // TODO: To check if this can be independent of rails and move with param sent from rails only for this case or can use from hub itself.
    accessKeyId: constants.LOGS_AWS_KEY,
    secretAccessKey: constants.LOGS_AWS_SECRET,
  });

  const s3 = new AWS.S3({ credentials, region: keyObject.logs_aws_region });
  const params = {
    Bucket: keyObject.logs_aws_bucket,
    Key: `${rails_session_id}/${rails_session_id}-lighthouse-report-${report_count}.html`,
    Expires: 604800,
  };

  try {
    const presignedUrl = s3.getSignedUrl('getObject', params);
    result.s3Url = presignedUrl;
  } catch (error) {
    HubLogger.miscLogger(TAG, `Error generating url: ${error.message}`, constants.LOG_LEVEL.ERROR);
  }
  return JSON.stringify(result);
}

const getValidDevtoolsPort = (product, keyObject) => {
  if (product == PLAYWRIGHT) {
    return keyObject.isLaunchPersistentContext ? REMOTE_DEBUGGER_PORT : PLAYWRIGHT_LH_PORT;
  } else {
    return REMOTE_DEBUGGER_PORT;
  }
}

const checkValidPlaywrightCommand = (message) => {
  if (message && message.method == 'goto') {
    return true;
  }
  return false;
};

const checkValidPuppeteerCommand = (message) => {
  if (message && message.method == 'Page.navigate') {
    return true;
  }
  return false;
};

const markSessionFailed = async (keyObject) => {
  const product = (keyObject.appTesting ? 'app-automate' : 'automate');
  const apiHandler = new RestAPIHandler(keyObject.rails_session_id, keyObject.appTesting);
  const body = JSON.stringify(LH_FAIL_SESSION_ARG);
  const headers = RestAPIHandler.DEFAULT_HEADERS;
  headers.Authorization = constants.AUTH_PREFIX + Buffer.from(`${keyObject.user}:${keyObject.accesskey}`).toString('base64');
  try {
    const response = await apiHandler.makeRequest(constants.PUT, `/${product}/sessions/${keyObject.rails_session_id}.json`, body, headers);
    const { data, statusCode } = response;
    if ( statusCode === 200 ) {
      Qig.markSessionStatusExecutor(keyObject);
    }
    HubLogger.miscLogger(TAG, `Update session status for ${keyObject.rails_session_id}`, constants.LOG_LEVEL.DEBUG);

    return statusCode === 200 ? true : false;
  } catch (e) {
    HubLogger.miscLogger(TAG, `Error marking session status: ${e.message}`, constants.LOG_LEVEL.ERROR);
    return false;
  }
}

const getCurrentOpenUrlOnChromium = async (product, keyObject) => {
  try {
    const bodyString = JSON.stringify({
      logHost: constants.zombie_server,
      path: jsonListEndpoint,
      request_type: "GET",
      port: getValidDevtoolsPort(product, keyObject)
    });
    const getOptions = {
      hostname: keyObject.rproxyHost,
      port: keyObject["os"].indexOf("win") > -1 ? "4567" : "45671",
      path: "/send_cdp_request_to_browser",
      agent: false,
      body: bodyString,
      method: 'POST',
      headers: {
        'content-type': 'application/json',
        accept: 'application/json',
        'content-length': Buffer.byteLength(bodyString, 'utf-8'),
      },
      timeout: timeout,
    };
    requestlib.appendBStackHostHeader(keyObject.name, getOptions.headers);
    const result = await requestlib.call(getOptions);
    if (result.statusCode != 200 || isUndefined(result.data)) {
      throw new Error(
        `Exception raised in terminal: ${JSON.stringify(result.data)}`
        );
    }
    const response = JSON.parse(result.data);
    if (isUndefined(response["cmd_executed"]) || response["cmd_executed"] !== "true") throw new Error("CDP command not executed")
    if (isUndefined(response["data"]) || !response["data"].length) throw new Error("Received no data from Terminal")
    // Filter page objects from the response. page -> browser tab
    response["data"] = response["data"].filter(item => item.type === "page")
    if (isUndefined(response["data"]) || !response["data"].length) throw new Error("Received no page data from Terminal")
    if (isUndefined(response["data"][0]) || isUndefined(response["data"][0].url)) throw new Error("Received corrupt page data from Terminal")

    return response["data"][0].url;
  } catch (error) {
    HubLogger.miscLogger(`lighthouse`, `Error occurred while getting open url. Error Details: ${error.message}`, constants.LOG_LEVEL.ERROR);
  }
}

const processCdpExecutorResponse = (jsonData) => {
  const performanceData = { enabled: false };
  if(isNotUndefined(jsonData.value) && isNotUndefined(jsonData.value.performanceLogs)) {
    if(isNotUndefined(jsonData.value.truncatePerformanceJson)) {
      performanceData.truncate = true;
      delete jsonData.value.truncatePerformanceJson;
    }
    performanceData.enabled = true;
    delete jsonData.value.performanceLogs;
  }
  return performanceData;
}

module.exports = { triggerLighthouse, checkValidPlaywrightCommand, checkValidPuppeteerCommand, lighthouseExecutorHandler, getPerformanceLogData, getCurrentOpenUrlOnChromium, processCdpExecutorResponse };
