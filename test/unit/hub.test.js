'use strict';

const rewire = require('rewire');
const hub = require('../../hub');
const WorkerPool = require('../../hubWorker/workerPoolManager');
const requestlib = require('../../lib/request');
const { assert, expect } = require('chai');
const sinon = require('sinon');
const ha = require('../../ha');
const originalHelper = require('../../helper');
const S3UploadHelper = require('../../helpers/s3UploadHelper');
const helper = require('./helper');
const helperFn = require('../../helper');
const constants = require('../../constants');
const errorMessageConstants = require('../../errorMessages');
const knox = constants.isTestingEnv ? require('faux-knox') : require('knox-s3'); // eslint-disable-line import/no-unresolved
const browserstack = require('../../browserstack');
const sessionManagerHelper = require('../../services/session/sessionManagerHelper');
const pubSub = require('../../pubSub');
const HubLogger = require('../../log');
const { isUndefined } = require('../../typeSanity');
const { isReqFromBrowserstack } = require('../../utils/verifyRequestHeaders');

const WebSocketHandler = rewire('../../webSocketHandler');

describe('longjhon', () => {
  let env;

  beforeEach(() => {
    env = constants.isProductionEnv;
  });

  afterEach(() => {
    constants.isProductionEnv = env;
  });

  it('should not require longjohn', () => {
    constants.isProductionEnv = true;
    hub.enableLongJohn();
  });
});

describe('blockedAt coverage', () => {
  let useBlockedAtVar;
  beforeEach(() => {
    useBlockedAtVar = constants.useBlockedAt;
  });
  afterEach(() => {
    constants.useBlockedAt = useBlockedAtVar;
    hub.stopBlockedAt();
  });
  it('should trigger blocked attack message', (done) => {
    constants.useBlockedAt = true;
    hub.usingBlockedAt(true);
    // Wait for blockedAt to initialize
    const initTimeout = setTimeout(() => {
      // Create a blocking operation that exceeds 50ms
      const startTime = Date.now();
      while (Date.now() - startTime < 100) {
        // Blocking loop for 100ms
      }
      const checkTimeout = setTimeout(() => {
        try {
          clearTimeout(initTimeout);
          clearTimeout(checkTimeout);
          done();
        } catch (err) {
          clearTimeout(initTimeout);
          clearTimeout(checkTimeout);
          done(err);
        }
      }, 10);
    }, 50);
  });
  it('should clean the existing blocked at', (done) => {
    constants.useBlockedAt = true;
    hub.usingBlockedAt(true);
    hub.usingBlockedAt(true);
    done();
  });
});

describe('processResponse', () => {
  const keyObject = { nonZeroStatusesCount: {} };

  beforeEach(() => {
    sinon.stub(HubLogger, 'sessionLog');
    sinon.stub(HubLogger, 'miscLogger');
    sinon.stub(originalHelper, 'timeoutManagerGetIndex');
    sinon.stub(S3UploadHelper, 'uploadScreenshotToS3').returns(JSON.stringify({ value: 'base64 encoded string' }));
  });

  afterEach(() => {
    HubLogger.sessionLog.restore();
    HubLogger.miscLogger.restore();
    originalHelper.timeoutManagerGetIndex.restore();
    S3UploadHelper.uploadScreenshotToS3.restore();
  });

  it('should call uploadScreenshotToS3 with JSON data', () => {
    const headers = { 'x-forwarded-for': '127.0.0.1' };
    const request = {
      id: 'random-id',
      headers,
      url: '/wd/hub/session/1a2b3c4d5e6f/screenshot',
      method: 'GET',
    };
    const response = { end: sinon.spy(), writeHead: sinon.spy(), write: sinon.spy() };
    const params = { data: JSON.stringify({ value: 'base64 encoded string' }), callbacks: {} };
    hub.processResponse(request, response, keyObject, params);
    assert(S3UploadHelper.uploadScreenshotToS3.calledOnce === true);
  });

  it('should handle response data correctly', () => {
    const request = {
      headers: {},
      url: '/test',
      method: 'GET',
    };
    const response = {
      called: {
        writeHead: false,
        write: false,
        end: false,
      },
      writeHead() {
        this.called.writeHead = true;
        return this;
      },
      write() {
        this.called.write = true;
        return this;
      },
      end() {
        this.called.end = true;
      },
    };
    const params = {
      data: 'test data',
      callbacks: {
        callbackEnd: sinon.spy(),
      },
    };
    hub.processResponse(request, response, keyObject, params);
    expect(response.called.writeHead || response.called.write || response.called.end).to.be.true;
  });

  it('should handle JSON response data', () => {
    const request = {
      headers: {},
      url: '/test-json',
      method: 'GET',
    };

    const response = {
      called: {
        writeHead: false,
        write: false,
        end: false,
      },
      writeHead() {
        this.called.writeHead = true;
        return this;
      },
      write() {
        this.called.write = true;
        return this;
      },
      end() {
        this.called.end = true;
      },
    };

    const params = {
      data: '{"value":"test"}',
      callbacks: {
        callbackEnd: sinon.spy(),
      },
    };

    hub.processResponse(request, response, keyObject, params);
    expect(response.called.writeHead || response.called.write || response.called.end).to.be.true;
  });

  it('should skip session ID replacement for screenshot request with single key response', () => {
    const request = {
      headers: {},
      url: '/screenshot',
      method: 'GET',
    };
    const response = {
      writeHead: sinon.spy(),
      write: sinon.spy(),
      end: sinon.spy(),
    };
    const params = {
      data: '{"value":"base64encoded"}',
      parsed_data: { value: 'base64encoded' },
      callbacks: { callbackEnd: sinon.spy() },
    };

    hub.processResponse(request, response, keyObject, params);
    expect(HubLogger.miscLogger.calledWith('SCREENSHOT-OPTIMIZE')).to.be.false;
  });

  it('should not call uploadScreenshotToS3 with JSON data for masked screenshot command', () => {
    const headers = { 'x-forwarded-for': '127.0.0.1' };
    const request = {
      id: 'random-id',
      headers,
      url: '/wd/hub/session/1a2b3c4d5e6f/screenshot',
      method: 'GET',
    };
    keyObject.maskCommands = ['screenshot'];
    keyObject.skip_screenshot_upload = true;

    const response = { end: sinon.spy(), writeHead: sinon.spy(), write: sinon.spy() };
    const params = { data: JSON.stringify({ value: 'base64 encoded string' }), callbacks: {} };
    hub.processResponse(request, response, keyObject, params);
    assert(S3UploadHelper.uploadScreenshotToS3.calledOnce === false);
  });

  it('should not call uploadScreenshotToS3 with JSON data for masked element screenshot command', () => {
    const headers = { 'x-forwarded-for': '127.0.0.1' };
    const request = {
      id: 'random-id',
      headers,
      url: '/wd/hub/session/1a2b3c4d5e6f/element/12dfknwoinfq233fgsg/screenshot',
      method: 'GET',
    };
    keyObject.maskCommands = ['screenshot'];
    keyObject.skip_screenshot_upload = true;

    const response = { end: sinon.spy(), writeHead: sinon.spy(), write: sinon.spy() };
    const params = { data: JSON.stringify({ value: 'base64 encoded string' }), callbacks: {} };
    hub.processResponse(request, response, keyObject, params);
    assert(S3UploadHelper.uploadScreenshotToS3.calledOnce === false);
  });
});

describe('Sensitive Commands Handling', () => {
  let request;
  let keyObject;
  let constantsMock;
  let HubLoggerMock;

  beforeEach(() => {
    request = {
      url: '',
      method: '',
      headers: {},
    };

    keyObject = {
      rails_session_id: 'test-session-id',
    };

    constantsMock = {
      global_registry: {
        'test-session-id': {
          maskCommands: [],
        },
      },
    };

    HubLoggerMock = {
      miscLogger: sinon.stub(),
    };

    global.constants = constantsMock;
    global.HubLogger = HubLoggerMock;
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should mark POST /value as sensitive if "setvalues" is in maskCommands', () => {
    request.url = '/session/test-session-id/element/123/value';
    request.method = 'POST';
    constantsMock.global_registry['test-session-id'].maskCommands = ['setvalues'];

    // Simulate the code
    if (
      request.url.match(/\/session\/[^/]+\/element\/[^/]+\/value$/) &&
      request.method === 'POST'
    ) {
      const sessionData = constantsMock.global_registry[keyObject.rails_session_id];
      if (
        sessionData &&
        sessionData.maskCommands &&
        Array.isArray(sessionData.maskCommands) &&
        sessionData.maskCommands.includes('setvalues')
      ) {
        request.headers['X-Appium-Is-Sensitive'] = 'true';
        HubLoggerMock.miscLogger(
          'SensitiveSetValues',
          `Sensitive command detected: ${request.url}`,
          'INFO'
        );
      }
    }

    // Assertions
    expect(request.headers['X-Appium-Is-Sensitive']).to.equal('true');
    sinon.assert.calledWith(
      HubLoggerMock.miscLogger,
      'SensitiveSetValues',
      `Sensitive command detected: ${request.url}`,
      'INFO'
    );
  });

  it('should not mark POST /value as sensitive if "setvalues" is not in maskCommands', () => {
    request.url = '/session/test-session-id/element/123/value';
    request.method = 'POST';

    // Simulate the code
    if (
      request.url.match(/\/session\/[^/]+\/element\/[^/]+\/value$/) &&
      request.method === 'POST'
    ) {
      const sessionData = constantsMock.global_registry[keyObject.rails_session_id];
      if (
        sessionData &&
        sessionData.maskCommands &&
        Array.isArray(sessionData.maskCommands) &&
        sessionData.maskCommands.includes('setvalues')
      ) {
        request.headers['X-Appium-Is-Sensitive'] = 'true';
        HubLoggerMock.miscLogger(
          'SensitiveSetValues',
          `Sensitive command detected: ${request.url}`,
          'INFO'
        );
      } else {
        HubLoggerMock.miscLogger(
          'SensitiveSetValues',
          `No mask commands for "setvalues" detected for session: ${keyObject.rails_session_id}`,
          'INFO'
        );
      }
    }

    expect(request.headers['X-Appium-Is-Sensitive']).to.be.undefined;
    sinon.assert.calledWith(
      HubLoggerMock.miscLogger,
      'SensitiveSetValues',
      `No mask commands for "setvalues" detected for session: ${keyObject.rails_session_id}`,
      'INFO'
    );
  });

  it('should mark GET /text as sensitive if "getvalues" is in maskCommands', () => {
    request.url = '/session/test-session-id/element/123/text';
    request.method = 'GET';
    constantsMock.global_registry['test-session-id'].maskCommands = ['getvalues'];

    if (
      request.url.match(/\/session\/[^/]+\/element\/[^/]+\/text$/) &&
      request.method === 'GET'
    ) {
      const sessionData = constantsMock.global_registry[keyObject.rails_session_id];
      if (
        sessionData &&
        sessionData.maskCommands &&
        Array.isArray(sessionData.maskCommands) &&
        sessionData.maskCommands.includes('getvalues')
      ) {
        request.headers['X-Appium-Is-Sensitive'] = 'true';
        HubLoggerMock.miscLogger(
          'SensitiveGetValues',
          `Sensitive GET command detected: ${request.url}`,
          'INFO'
        );
      }
    }

    expect(request.headers['X-Appium-Is-Sensitive']).to.equal('true');
    sinon.assert.calledWith(
      HubLoggerMock.miscLogger,
      'SensitiveGetValues',
      `Sensitive GET command detected: ${request.url}`,
      'INFO'
    );
  });

  it('should not mark GET /text as sensitive if "getvalues" is not in maskCommands', () => {
    request.url = '/session/test-session-id/element/123/text';
    request.method = 'GET';

    // Simulate the code
    if (
      request.url.match(/\/session\/[^/]+\/element\/[^/]+\/text$/) &&
      request.method === 'GET'
    ) {
      const sessionData = constantsMock.global_registry[keyObject.rails_session_id];
      if (
        sessionData &&
        sessionData.maskCommands &&
        Array.isArray(sessionData.maskCommands) &&
        sessionData.maskCommands.includes('getvalues')
      ) {
        request.headers['X-Appium-Is-Sensitive'] = 'true';
        HubLoggerMock.miscLogger(
          'SensitiveGetValues',
          `Sensitive GET command detected: ${request.url}`,
          'INFO'
        );
      } else {
        HubLoggerMock.miscLogger(
          'SensitiveGetValues',
          `No mask commands for "getValues" detected for session: ${keyObject.rails_session_id}`,
          'INFO'
        );
      }
    }

    expect(request.headers['X-Appium-Is-Sensitive']).to.be.undefined;
    sinon.assert.calledWith(
      HubLoggerMock.miscLogger,
      'SensitiveGetValues',
      `No mask commands for "getValues" detected for session: ${keyObject.rails_session_id}`,
      'INFO'
    );
  });
});

describe('Test idletimeout', () => {
  const sessionId = '1a2b3c4d5e6f';
  const hostname = '************';
  const basicAuth = ['test', 'test'];
  const request = { id: 'random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
  let caps = {};
  let options = {
    bsCaps: {},
    browserstackParams: {
      'browserstack.aws.save': 'bs-stag/1a2b3c4d5e6f',
      'browserstack.tunnel': 'false',
    },
  };
  const callBack = null;

  beforeEach(() => {
    sinon.stub(knox, 'createClient').returns({});
    sinon.stub(originalHelper, 'takeScreenshotAndUpload');
    sinon.stub(ha, 'setData');
    caps = {};
    options = {
      bsCaps: {},
      browserstackParams: {
        'browserstack.aws.save': 'bs-stag/1a2b3c4d5e6f',
        'browserstack.tunnel': 'false',
      },
    };
  });

  it('add idletimeout to global registry', () => {
    caps['browserstack.idleTimeout'] = 60;
    options.browserstackParams['browserstack.idleTimeout'] = 60;
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.idle_timeout, 60000);
    assert.equal(res.newCommandTimeout, 0);
  });

  it('add default idletimeout, newCommandTimeout to global registry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.idle_timeout, 90000);
    assert.equal(res.newCommandTimeout, 0);
  });

  it('add hoothootCanaryTags to global registry', () => {
    options.hoothootCanaryTags = { tag: 'test' };

    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.hoothootCanaryTags, options.hoothootCanaryTags);
  });

  it('add default server_port as null to global registry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.server_port, null);
  });

  it('add server_port based on request header to global registry', () => {
    const testReq = { id: 'random-id', headers: { 'x-forwarded-for': '127.0.0.1', 'x-server-port': 80 } };
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      testReq, basicAuth, callBack
    );
    assert.equal(res.server_port, 80);
  });

  afterEach(() => {
    originalHelper.takeScreenshotAndUpload.restore();
    knox.createClient.restore();
    ha.setData.restore();
  });
});

describe('Test translate localhost url', () => {
  const sessionId = '1a2b3c4d5e6f';
  const hostname = '************';
  const basicAuth = ['test', 'test'];
  const request = { id: 'random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
  let caps = {};
  let options = {};
  const callBack = null;

  beforeEach(() => {
    caps = {};
    options = {
      bsCaps: {},
      browserstackParams: {
        'browserstack.aws.save': 'bs-stag/1a2b3c4d5e6f',
        'browserstack.tunnel': 'false',
      },
    };
  });

  it('add translateLocalhostUrl to global registry', () => {
    options.browserstackParams.translate_localhost_url = true;
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.translateLocalhostUrl, true);
  });

  it('should return translateLocalhostUrl undefined if browserstackParams does not contain translate localhost url cap', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(isUndefined(res.translateLocalhostUrl), true);
  });
});

describe('#pushHubServerTimeoutDataToZombieAndHH', () => {
  it('should send data to both zombies and hh', () => {
    const x = sinon.spy(originalHelper, 'PingZombie');
    const y = sinon.spy(originalHelper, 'hoothootPusher');

    hub.pushHubServerTimeoutDataToZombieAndHH();

    assert(x.calledOnce === true);
    assert(y.calledOnce === true);

    originalHelper.PingZombie.restore();
    originalHelper.hoothootPusher.restore();
  });
});

describe('#handleGetSessionData function', () => {
  let keyObject;
  beforeEach(() => {
    keyObject = helper.getKeyObject();
    constants.global_registry[keyObject.rails_session_id] = keyObject;
  });

  afterEach(() => {
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('sould return with found false when session is pending delete', () => {
    const response = {
      end: sinon.stub(),
    };
    const expectedResponseData = '{"found":false}';
    keyObject.pendingDelete = true;

    hub.handleGetSessionData(response, keyObject.rails_session_id);
    sinon.assert.calledWith(response.end, expectedResponseData);
  });

  it('should return session data if key present and not pendingdelete session', () => {
    const response = {
      end: sinon.stub(),
    };
    keyObject.found = true;
    const expectedResponseData = JSON.stringify(keyObject);
    sinon.stub(originalHelper, 'sessionRemovedFromRegionHook');
    sinon.stub(pubSub, 'publish');

    hub.handleGetSessionData(response, keyObject.rails_session_id);
    sinon.assert.calledWith(response.end, expectedResponseData);
    assert(originalHelper.sessionRemovedFromRegionHook.called.should.be.true);
    // session deletion publish call moved inside sessionRemovedFromRegionHook
    assert(pubSub.publish.called.should.be.false);
    originalHelper.sessionRemovedFromRegionHook.restore();
    pubSub.publish.restore();
  });

  it('should return with found false if session not in global registry and redis', () => {
    const response = {
      end: sinon.stub(),
    };
    const expectedResponseData = '{"found":false}';
    sinon.stub(ha, 'getData').yields(false, {});

    hub.handleGetSessionData(response, 'random');
    sinon.assert.calledWith(response.end, expectedResponseData);
    ha.getData.restore();
  });

  it('should return session data if session not in global registry but in redis', () => {
    const response = {
      end: sinon.stub(),
    };
    keyObject.found = true;
    const expectedResponseData = JSON.stringify(keyObject);
    sinon.stub(ha, 'getData').yields(false, keyObject);
    sinon.stub(originalHelper, 'sessionRemovedFromRegionHook');
    sinon.stub(pubSub, 'publish');

    hub.handleGetSessionData(response, 'random');
    sinon.assert.calledWith(response.end, expectedResponseData);
    assert(originalHelper.sessionRemovedFromRegionHook.called.should.be.true);
    // session deletion publish call moved inside sessionRemovedFromRegionHook
    assert(pubSub.publish.called.should.be.false);
    originalHelper.sessionRemovedFromRegionHook.restore();
    pubSub.publish.restore();
    ha.getData.restore();
  });
});

describe('#sessionNotFound function', () => {
  it('Responds with status code 500 and message \'Invalid Command\' if host_params is false', () => {
    const response = {
      writeHead: sinon.stub(),
      end: sinon.stub(),
    };
    const expectedResponseData = JSON.stringify({
      value: {
        message: 'Invalid Command',
      },
    });
    hub.sessionNotFound(response, false);
    sinon.assert.calledWith(response.end, expectedResponseData);
  });

  it('Responds with status code 500 and message \'Session not started or terminated\' if host_params is not false', () => {
    const response = {
      writeHead: sinon.stub(),
      end: sinon.stub(),
    };
    const expectedResponseData = JSON.stringify({
      value: {
        message: 'Session not started or terminated',
      },
      sessionId: '',
      status: 13,
    });

    hub.sessionNotFound(response, {
      rails_session_id: 'SOME_RANDOM_ID',
    });
    sinon.assert.calledWith(response.end, expectedResponseData);
  });
});


describe('#regSession()', () => {
  let keyObject;
  before(() => {
    keyObject = helper.getKeyObject();
    keyObject.os = 'MAC';
    keyObject.video_start_time = 10;
    keyObject.platform_video_start_time = 10;
    constants.global_registry[keyObject.rails_session_id] = keyObject;
  });

  after(() => {
    delete constants.global_registry[keyObject.rails_session_id];
  });

  it('should trigger postBrowserStack', () => {
    const regSessionOptions = {
      build_number: 10,
    };
    sinon.stub(browserstack, 'postBrowserStack');
    hub.regSession(keyObject.rails_session_id, 10, '127.0.0.1', 10, {}, false, regSessionOptions);
    assert(browserstack.postBrowserStack.called.should.be.true);
    browserstack.postBrowserStack.restore();
  });

  it('should trigger postBrowserStack when recoveredBrowserMobError & selenium_version', () => {
    const regSessionOptions = {
      build_number: 10,
      recoveredBrowserMobError: true,
      selenium_version: '3.141.0',
    };
    sinon.stub(browserstack, 'postBrowserStack');
    hub.regSession(keyObject.rails_session_id, 10, '127.0.0.1', 10, {}, false, regSessionOptions);
    assert(browserstack.postBrowserStack.called.should.be.true);
    browserstack.postBrowserStack.restore();
  });
});

describe('#timeoutCb', () => {
  it('should try to delete socket on timeout', (done) => {
    hub.timeoutCb({
      destroy: () => {
        done();
      },
    });
  });
  it('should handle exception in the method', (done) => {
    hub.timeoutCb({
      destroy: () => {
        throw new Error('random exception');
      },
    });
    done();
  });
});

describe('delete_rails_caps', () => {
  it('should delete necessary caps if exceeds limit', () => {
    const originalLimit = constants.railsDeleteLength;
    constants.railsDeleteLength = 10;
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {
          'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
        },
      },
      'bstack:options': {
        chromeOptions: {
          prefs: {
            'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
          },
        },
      },
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const capsPostDelete = {
      browser: 'chrome',
      browser_version: '120',
      'bstack:options': {},
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {},
        }],
        alwaysMatch: {
          'bstack:options': {},
        },
      },
    };
    const newCaps = hub.delete_rails_caps(JSON.stringify(caps), caps);
    assert(newCaps === JSON.stringify(capsPostDelete));
    constants.railsDeleteLength = originalLimit;
  });

  it('should not delete necessary caps if within limits', () => {
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {
          'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
        },
      },
    };
    const newCaps = hub.delete_rails_caps(JSON.stringify(caps), caps);
    assert(newCaps === JSON.stringify(caps));
  });

  it('should handle error and return original string', () => {
    const originalLimit = constants.railsDeleteLength;
    constants.railsDeleteLength = 10;
    const pingZombieStub = sinon.stub(originalHelper, 'PingZombie').throws(new Error('some error'));
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: 'error',
      },
    };
    const newCaps = hub.delete_rails_caps(JSON.stringify(caps), caps);
    assert(newCaps === JSON.stringify(caps));
    pingZombieStub.restore();
    constants.railsDeleteLength = originalLimit;
  });
});

describe('deleteRailsCapsWithCharLimit', () => {
  it('should delete params within the caps if exceeds limit', () => {
    const originalLimit = constants.railsPerCapMaxLength;
    constants.railsPerCapMaxLength = 20;
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {
          'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
        },
      },
      'bstack:options': {
        chromeOptions: {
          prefs: {
            'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
          },
        },
      },
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Download',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const capsPostDelete = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {},
      },
      'bstack:options': {
        chromeOptions: {
          prefs: {},
        },
      },
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              prefs: {},
            },
          },
          chromeOptions: {
            prefs: {},
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {},
            },
          },
          chromeOptions: {
            prefs: {},
          },
        },
      },
    };
    const newCaps = hub.deleteRailsCapsWithCharLimit(caps, 0);
    assert(JSON.stringify(newCaps) === JSON.stringify(capsPostDelete));
    constants.railsPerCapMaxLength = originalLimit;
  });

  it('should return the default caps for further objects if maxDepth is reached', () => {
    const originalLimit = constants.railsPerCapMaxLength;
    constants.railsPerCapMaxLength = 20;
    const originalDepth = constants.railsDeleteCapsMethodMaxDepth;
    constants.railsDeleteCapsMethodMaxDepth = 5;
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {
          'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
        },
      },
      'bstack:options': {
        chromeOptions: {
          prefs: {
            'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
          },
        },
      },
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Download',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const capsPostDelete = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {},
      },
      'bstack:options': {
        chromeOptions: {
          prefs: {},
        },
      },
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Download',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const newCaps = hub.deleteRailsCapsWithCharLimit(caps, 0);
    assert(JSON.stringify(newCaps) === JSON.stringify(capsPostDelete));
    constants.railsPerCapMaxLength = originalLimit;
    constants.railsDeleteCapsMethodMaxDepth = originalDepth;
  });

  it('should not delete params if within limits', () => {
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      chromeOptions: {
        prefs: {
          'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
        },
      },
      'bstack:options': {
        chromeOptions: {
          prefs: {
            'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
          },
        },
      },
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Download',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const newCaps = hub.deleteRailsCapsWithCharLimit(caps, 0);
    assert(JSON.stringify(newCaps) === JSON.stringify(caps));
  });

  it('should remove duplicate elements in any array', () => {
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef'],
            },
          },
          chromeOptions: {
            args: ['--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef'],
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef'],
            },
          },
          chromeOptions: {
            args: ['--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef', '--test-default', '--dummay-arg', '--abcdef'],
          },
        },
      },
    };
    const capsPostDelete = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg', '--abcdef'],
            },
          },
          chromeOptions: {
            args: ['--test-default', '--dummay-arg', '--abcdef'],
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg', '--abcdef'],
            },
          },
          chromeOptions: {
            args: ['--test-default', '--dummay-arg', '--abcdef'],
          },
        },
      },
    };
    const newCaps = hub.deleteRailsCapsWithCharLimit(caps, 0);
    assert(JSON.stringify(newCaps) === JSON.stringify(capsPostDelete));
  });

  it('should handle error and return original string', () => {
    const isArrayStub = sinon.stub(Array, 'isArray').throws(new Error('Some error'));
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg'],
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const newCaps = hub.deleteRailsCapsWithCharLimit(caps, 20);
    assert(JSON.stringify(newCaps) === JSON.stringify(caps));
    isArrayStub.restore();
  });
});

describe('sanitiseRailsCapsLimit', () => {
  it('should return orig caps if caps length is less tha max Delete length', () => {
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg'],
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const newCaps = hub.sanitiseRailsCapsLimit(JSON.stringify(caps), caps);
    assert(newCaps === JSON.stringify(caps));
  });
  it('should sanitise caps with deleteRailsCapsWithCharLimit and delete_rails_caps if greater than max length', () => {
    const originalLimit = constants.railsDeleteLength;
    constants.railsDeleteLength = 10;
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg', '--dummay-arg'],
            },
            default_content_settings: {
              popups: 0,
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
            default_content_settings: {
              popups: 0,
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const resultCaps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            default_content_settings: {
              popups: 0,
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {},
        },
      },
    };
    const pingZombieStub = sinon.stub(originalHelper, 'PingZombie');
    const newCaps = hub.sanitiseRailsCapsLimit(JSON.stringify(caps), caps);
    sinon.assert.called(pingZombieStub);
    assert(newCaps === JSON.stringify(resultCaps));
    constants.railsDeleteLength = originalLimit;
    pingZombieStub.restore();
  });
  it('should return actual caps if error is thrown', () => {
    const originalLimit = constants.railsDeleteLength;
    constants.railsDeleteLength = 10;
    const pingZombieStub = sinon.stub(originalHelper, 'PingZombie').throws(new Error('some error'));
    const caps = {
      browser: 'chrome',
      browser_version: '120',
      W3C_capabilities: {
        firstMatch: [{
          'bstack:options': {
            chromeOptions: {
              args: ['--test-default', '--dummay-arg'],
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Download',
            },
          },
        }],
        alwaysMatch: {
          'bstack:options': {
            chromeOptions: {
              prefs: {
                'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
              },
            },
          },
          chromeOptions: {
            prefs: {
              'download.default_directory': 'c:\\Users\\<USER>\\Downloads',
            },
          },
        },
      },
    };
    const capsString = JSON.stringify(caps);
    const newCaps = hub.sanitiseRailsCapsLimit(capsString, caps);
    assert(newCaps === capsString);
    constants.railsDeleteLength = originalLimit;
    pingZombieStub.restore();
  });
});

describe('#isPageLoadTimeoutBrowser', () => {
  it('returns true if browser is chrome, ie or firefox', () => {
    assert(hub.isPageLoadTimeoutBrowser('internet explorer'));
    assert(hub.isPageLoadTimeoutBrowser('chrome'));
    assert(hub.isPageLoadTimeoutBrowser('firefox'));
    assert(hub.isPageLoadTimeoutBrowser('safari') === false);
  });
});

describe('#checkIfRequestIsNotValid', () => {
  it('returns false if request invalid', () => {
    constants.not_allowed_requests = ['POST:maximize'];
    assert(hub.checkIfRequestIsNotValid('POST:maximize'));
    assert(hub.checkIfRequestIsNotValid('GET:element') === false);
  });
});

describe('#checkIfNonPipeUrl', () => {
  it('returns false if non-piped url', () => {
    constants.not_allowed_requests = ['POST:value'];
    assert(hub.checkIfNonPipeUrl('POST:value'));
    assert(hub.checkIfNonPipeUrl('GET:element') === false);
  });
});

describe('#isPageLoadTimeoutError', () => {
  it('returns true if pageLoad payload status code is 28 else returns false', () => {
    const pageLoadPayload = {};
    pageLoadPayload.status = 28;
    assert(hub.isPageLoadTimeoutError(pageLoadPayload));
    pageLoadPayload.status = 10;
    assert(hub.isPageLoadTimeoutError(pageLoadPayload) === false);
  });
});

describe('#isPageLoadTimeoutErrorType', () => {
  it('should return true if pageLoad payload status code is 28 and 21, else returns false', () => {
    const pageLoadPayload = {};
    pageLoadPayload.status = 28;
    assert(hub.isPageLoadTimeoutErrorType(pageLoadPayload));
    pageLoadPayload.status = 21;
    assert(hub.isPageLoadTimeoutErrorType(pageLoadPayload));
    pageLoadPayload.status = 10;
    assert(hub.isPageLoadTimeoutErrorType(pageLoadPayload) === false);
  });
  it('should return true if pageLoad payload has timeout errors, else false', () => {
    const pageLoadPayload = { status: 13, state: 'unhandled error', message: 'Error loading page, timed out' };
    assert(hub.isPageLoadTimeoutErrorType(pageLoadPayload));
    const pageLoadPayload2 = { value: { message: 'timeout: Timed out receiving message from renderer:' } };
    assert(hub.isPageLoadTimeoutErrorType(pageLoadPayload2));
    const pageLoadPayload3 = { value: { error: 'timeout', message: '' } };
    assert(hub.isPageLoadTimeoutErrorType(pageLoadPayload3) === false);
    const successfulLoad = { status: 'success', value: null };
    assert(hub.isPageLoadTimeoutErrorType(successfulLoad) === false);
  });
});

describe('truncateHosts', () => {
  it('should not add truncate hosts when caps is empty', (done) => {
    const caps = {};
    hub.truncateHosts(caps, null);
    assert(originalHelper.isNonEmptyHash(caps) === false);
    done();
  });
});

describe('#initSessionDeletionSubscriber #initIdleTimeoutSub', () => {
  it('should call ha.deleteData removeFromMemory', () => {
    const keyObject = helper.getKeyObject();
    constants.global_registry[keyObject.rails_session_id] = keyObject;

    const removeFromMemory = sinon.stub(sessionManagerHelper, 'removeFromMemory');
    const subscribe = sinon.stub(pubSub, 'subscribe').callsArgWith(1, keyObject.rails_session_id);

    this.clock = sinon.useFakeTimers();
    hub.initIdleTimeoutSub();
    hub.initSessionDeletionSubscriber();
    this.clock.tick(15010);
    assert(removeFromMemory.called.should.be.true);
    removeFromMemory.restore();
    subscribe.restore();
    this.clock.restore();
  });
});

describe('#getUserAgent', () => {
  const request = { headers: { 'User-Agent': 'curl/7.54.0' } };
  const requestWithoutUserAgent = { headers: { Accept: 'application/json' } };

  it('should return the user agent from the request', () => {
    const userAgent = hub.getUserAgent(request);
    assert(userAgent, 'curl/7.54.0');
  });

  it('should return the undefined if user agent not set in the request', () => {
    const userAgent = hub.getUserAgent(requestWithoutUserAgent);
    assert(isUndefined(userAgent), true);
  });
});

describe('#truncateHosts', () => {
  let caps;
  let railsOmittedCaps;
  beforeEach(() => {
    caps = {
      desiredCapabilities: {
        'bstack:options': {
          os: 'Windows',
          osVersion: '7',
          local: 'false',
        },
        browserName: 'IE',
        browserVersion: '8.0',
        'browserstack.hosts': '127.0.0.1 w3schools.com 127.0.0.1 www.w3schools.com 127.0.0.1 www.google.com 127.0.0.1 google.com 127.0.0.1 www.ebay.com 127.0.0.1 - ebay.com',
      },
    };
    railsOmittedCaps = {};
  });

  it('should truncate hosts to length 25', () => {
    hub.truncateHosts(caps, railsOmittedCaps);
    assert.equal(caps.desiredCapabilities['browserstack.hosts'], '127.0.0.1 w3schools.com 1...');
  });

  it('should not truncate hosts of length < 25', () => {
    const host = '******* demo';
    caps.desiredCapabilities['browserstack.hosts'] = host;
    hub.truncateHosts(caps, railsOmittedCaps);
    assert.equal(caps.desiredCapabilities['browserstack.hosts'], host);
  });

  it('should add browserstack.hosts to rails_omitted_caps', () => {
    hub.truncateHosts(caps, railsOmittedCaps);
    assert.notEqual(undefined, railsOmittedCaps['browserstack.hosts']);
  });

  it('should do nothing is browserstack.hosts is not present', () => {
    delete caps.desiredCapabilities['browserstack.hosts'];
    hub.truncateHosts(caps, railsOmittedCaps);
    assert.equal(JSON.stringify(caps), JSON.stringify(caps));
    assert.equal(JSON.stringify(railsOmittedCaps), JSON.stringify({}));
  });
});

describe('#restoreHosts', () => {
  let caps;
  let options;
  beforeEach(() => {
    caps = {
      desiredCapabilities: {
        'browserstack.hosts': '127.0.0.1 w3schools.com...',
      },
    };
    options = {
      browserstackParams: {
        'browserstack.hosts': '127.0.0.1 w3schools.com 127.0.0.1 www.w3schools.com 127.0.0.1 www.google.com 127.0.0.1 google.com 127.0.0.1 www.ebay.com 127.0.0.1 - ebay.com',
      },
    };
  });

  it('should copy hosts from options to caps', () => {
    hub.restoreHosts(caps, options);
    assert.equal(caps.desiredCapabilities['browserstack.hosts'], options.browserstackParams['browserstack.hosts']);
  });

  it('should return undefined if hosts is missing in caps', () => {
    delete caps.desiredCapabilities['browserstack.hosts'];
    const truncateHosts = hub.restoreHosts(caps, options);
    assert.equal(truncateHosts, undefined);
  });

  it('should not copy hosts if missing in options', () => {
    delete options.browserstackParams['browserstack.hosts'];
    const originalHosts = caps.desiredCapabilities['browserstack.hosts'];
    hub.restoreHosts(caps, options);
    assert.equal(originalHosts, caps.desiredCapabilities['browserstack.hosts']);
  });
});

describe('Test clientConnectionSocketsCount', () => {
  const sessionId = '1a2b3c4d5e6f';
  const hostname = '************';
  const basicAuth = ['test', 'test'];
  const requestWithConnectionRequests = { id: 'random-id', headers: { 'x-forwarded-for': '127.0.0.1', 'x-connection-requests': '1' } };
  const requestWithoutConnectionRequests = { id: 'random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
  let caps = {};
  let options = {};
  const callBack = null;

  beforeEach(() => {
    caps = {};
    options = {
      bsCaps: {},
      browserstackParams: {
        'browserstack.aws.save': 'bs-stag/1a2b3c4d5e6f',
        'browserstack.tunnel': 'false',
      },
    };
  });

  it('add clientConnectionSocketsCount to global registry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      requestWithConnectionRequests, basicAuth, callBack
    );
    assert.equal(res.clientConnectionSocketsCount, 1);
  });

  it('should return clientConnectionSocketsCount 0 if request does not contain x-connection-requests in headers', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      requestWithoutConnectionRequests, basicAuth, callBack
    );
    assert.equal(res.clientConnectionSocketsCount, 0);
  });
});

describe('#addNewMappingInRegistry', () => {
  const sessionId = '1a2b3c4d5e6f';
  const hostname = '************';
  const basicAuth = ['test', 'test'];
  const request = { id: 'random-id', headers: { 'x-forwarded-for': '127.0.0.1' } };
  let caps = {};
  let options = {};
  const callBack = null;

  beforeEach(() => {
    caps = {};
    options = {
      bsCaps: {},
      browserstackParams: {
        'browserstack.aws.save': 'bs-stag/1a2b3c4d5e6f',
        'browserstack.tunnel': 'false',
      },
    };
  });

  it('sets markedAsPercy as false while creating a new entry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.markedAsPercy, false);
  });

  it('sets percyBeginTime as -1 while creating a new entry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.percyBeginTime, -1);
  });

  it('sets percyNumberOfTiles as 0 while creating a new entry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.percyNumberOfTiles, 0);
  });

  it('should add platformDetails in keyObject', () => {
    const platformDetails = {
      os: 'some os',
      version: 'some version',
    };
    options.platformDetails = { ...platformDetails };
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.deepEqual(res.platformDetails, platformDetails);
  });

  it('should add aiEnabledSessions in keyObject', () => {
    const platformDetails = {
      os: 'some os',
      version: 'some version',
    };
    options.platformDetails = { ...platformDetails };
    options.browserstackParams['browserstack.ai_enabled_session'] = 'true';
    options.browserstackParams['browserstack.ai_details'] = '{}';
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    assert.equal(res.aiEnabledSessions, true);
    assert.deepEqual(res.aiSessionDetails, JSON.parse(options.browserstackParams['browserstack.ai_details']));
  });

  it('sets aiTcgDetails while creating a new entry', () => {
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, options,
      request, basicAuth, callBack
    );
    const result = JSON.stringify({
      region: constants.region,
      tcgUrls: constants.TCG_SERVICE.regions,
    });
    assert.equal(res.aiTcgDetails, result);
  });

  it('sets aiSessionDetails while creating a new entry', () => {
    const opts = { ...options };
    opts.browserstackParams['browserstack.ai_enabled_session'] = 'true';
    opts.browserstackParams['browserstack.ai_details'] = JSON.stringify({
      tcgUrl: 'example.com',
    });
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, opts,
      request, basicAuth, callBack
    );
    assert.equal(res.aiEnabledSessions, true);
    assert.deepEqual(res.aiSessionDetails, JSON.parse(opts.browserstackParams['browserstack.ai_details']));
  });

  it('sets enableCameraVideoInjection while creating a new entry', () => {
    const opts = { ...options };
    opts.browserstackParams['browserstack.enableCameraVideoInjection'] = 'true';
    const res = hub.addNewMappingInRegistry(
      sessionId, hostname, caps, opts,
      request, basicAuth, callBack
    );
    assert.equal(res.enableCameraVideoInjection, true);
  });
});

describe('#checkAndSetLocalNudge', () => {
  describe('in case of non-safari browsers', () => {
    let output;
    let keyObject;
    let request;

    beforeEach(() => {
      output = {
        statusCode: 500,
        data: '{"value":{"message":"unknown error: net::ERR_NAME_NOT_RESOLVED"}}',
      };
      keyObject = {
        rails_session_id: 'abcdef',
        userPassedLocalCap: false,
        nudgeLocalNotSetError: '',
        browser: 'chrome',
      };
      request = {
        log_data: '{"url":"http://some.private.url.abcdef/"}',
      };
      sinon.stub(HubLogger, 'miscLogger', () => {});
    });

    afterEach(() => {
      HubLogger.miscLogger.restore();
    });

    it('should set nudgeLocalNotSetError if all conditions met', () => {
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, 'some.private.url.abcdef');
    });

    it('should not set nudgeLocalNotSetError if local true', () => {
      keyObject.userPassedLocalCap = true;
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
    });

    it('should not set nudgeLocalNotSetError if no error in response', () => {
      output.data = '{"value":{"message":"no error"}}';
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
    });

    it('should not set nudgeLocalNotSetError if status is not 500', () => {
      output.statusCode = 200;
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
    });

    it('should call logger in catch if malformed data', () => {
      output.data = 'malformedJSON';
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
      assert(HubLogger.miscLogger.calledOnce);
    });
  });

  describe('in case of safari browser', () => {
    let output;
    let keyObject;
    let request;

    beforeEach(() => {
      output = {
        statusCode: 200,
        data: '{"value":"Failed to open page"}',
      };
      keyObject = {
        rails_session_id: 'abcdef',
        tunnel: true,
        userPassedLocalCap: false,
        nudgeLocalNotSetError: '',
        browser: 'safari',
        lastOpenedUrl: 'http://some.private.url.abcdef/',
      };
      sinon.stub(HubLogger, 'miscLogger', () => {});
    });

    afterEach(() => {
      HubLogger.miscLogger.restore();
    });

    it('should set nudgeLocalNotSetError if all conditions met', () => {
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, 'some.private.url.abcdef');
    });

    it('should not set nudgeLocalNotSetError if not error in output', () => {
      output.data = '{"value":""}';
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
    });

    it('should not set nudgeLocalNotSetError if local true', () => {
      keyObject.userPassedLocalCap = true;
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
    });

    it('should call logger in catch if malformed data', () => {
      output.data = 'malformedJSON';
      hub.checkAndSetLocalNudge(keyObject, output, request);
      assert.equal(keyObject.nudgeLocalNotSetError, '');
      assert(HubLogger.miscLogger.calledOnce);
    });
  });

  describe('#setWorkerId', () => {
    beforeEach(() => {
      constants.worker_id = 'temp_worker_id';
    });
    afterEach(() => {
      delete constants.worker_id;
    });
    it('should set workerId', () => {
      hub.setWorkerId('worker_id');
      assert.equal(constants.worker_id, 'worker_id');
    });
  });

  describe('#setShouldDieKeepAliveHeader', () => {
    beforeEach(() => {
      constants.shouldDieKeepAlive = false;
    });
    afterEach(() => {
      delete constants.shouldDieKeepAlive;
    });
    it('should set setShouldDieKeepAliveHeader', () => {
      hub.setShouldDieKeepAliveHeader();
      assert.equal(constants.shouldDieKeepAlive, true);
    });
  });
});

describe('trapGlobalException', () => {
  let exceptionLoggerStub;
  let hoothootEmitStub;
  let seleniumStatsStub;

  beforeEach(() => {
    exceptionLoggerStub = sinon.stub(HubLogger, 'exceptionLogger');
    hoothootEmitStub = sinon.stub(HubLogger.hoothoot, 'emit');
    seleniumStatsStub = sinon.stub(HubLogger, 'seleniumStats');
  });

  afterEach(() => {
    exceptionLoggerStub.restore();
    hoothootEmitStub.restore();
    seleniumStatsStub.restore();
  });

  it('should handle global exceptions 1', () => {
    constants.isProductionEnv = true;
    constants.USE_LONGJOHN = true;
    const err = { stack: 'test Error ------------------' };
    hub.trapGlobalException(err);

    expect(exceptionLoggerStub.calledOnce).to.be.true;
    expect(hoothootEmitStub.calledOnce).to.be.true;
    expect(seleniumStatsStub.calledOnce).to.be.true;
    constants.isProductionEnv = false;
    constants.USE_LONGJOHN = false;
  });

  it('should handle global exceptions 2', () => {
    constants.isProductionEnv = true;
    const err = { stack: 'test Error' };
    hub.trapGlobalException(err);

    expect(exceptionLoggerStub.calledOnce).to.be.true;
    expect(hoothootEmitStub.calledOnce).to.be.true;
    expect(seleniumStatsStub.calledOnce).to.be.true;
    constants.isProductionEnv = false;
  });

  it('should handle global exceptions 3', () => {
    constants.isProductionEnv = true;
    const err = { stack: 'takeScreenshotAndUpload ' };
    hub.trapGlobalException(err);

    expect(exceptionLoggerStub.calledOnce).to.be.true;
    expect(hoothootEmitStub.calledOnce).to.be.true;
    expect(seleniumStatsStub.calledOnce).to.be.true;
    constants.isProductionEnv = false;
  });
});

describe('closeWorkerPool', () => {
  let clock;
  let closeStub;

  beforeEach(() => {
    clock = sinon.useFakeTimers();
    closeStub = sinon.stub(WorkerPool, 'close');
  });

  afterEach(() => {
    clock.restore();
    closeStub.restore();
  });

  it('should close the worker pool after a delay', () => {
    hub.closeWorkerPool();
    expect(closeStub.called).to.be.false;

    clock.tick(constants.workerCloseDelay);
    expect(closeStub.calledOnce).to.be.true;
  });
});

describe('handleCallbackDone', () => {
  let readRequestStub;
  let publishStub;
  let miscLoggerStub;
  let request;
  let response;

  beforeEach(() => {
    readRequestStub = sinon.stub(requestlib, 'readRequest');
    publishStub = sinon.stub(pubSub, 'publish');
    miscLoggerStub = sinon.stub(HubLogger, 'miscLogger');
    request = {};
    response = { end: sinon.spy() };
  });

  afterEach(() => {
    readRequestStub.restore();
    publishStub.restore();
    miscLoggerStub.restore();
  });

  it('should handle callback done successfully', async () => {
    const req_data = { test: 'data' };
    readRequestStub.returns(Promise.resolve(req_data));

    await hub.handleCallbackDone(request, response, 'testSessionId', 'testUser', 'testTerminal');
    assert(response.end.calledOnce === true);
  });
});

describe('handleCheckUrlReq', () => {
  let readRequestStub;
  let publishStub;
  let miscLoggerStub;
  let request;
  let response;

  beforeEach(() => {
    readRequestStub = sinon.stub(requestlib, 'readRequest');
    publishStub = sinon.stub(pubSub, 'publish');
    miscLoggerStub = sinon.stub(HubLogger, 'miscLogger');
    request = {};
    response = { end: sinon.spy() };
  });

  afterEach(() => {
    readRequestStub.restore();
    publishStub.restore();
    miscLoggerStub.restore();
  });

  it('should handle check url request successfully', async () => {
    const req_data = { test: 'data' };
    readRequestStub.returns(Promise.resolve(req_data));

    await hub.handleCheckUrlReq(request, response, 'testSessionId', 'testUser', 'testTerminal');
    assert(response.end.calledOnce === true);
  });
});


describe('handleXDRPC', () => {
  let readRequestStub;
  let requestCallStub;
  let miscLoggerStub;
  let request;
  let response;

  beforeEach(() => {
    readRequestStub = sinon.stub(requestlib, 'readRequest');
    requestCallStub = sinon.stub(requestlib, 'call');
    miscLoggerStub = sinon.stub(HubLogger, 'miscLogger');
    request = { headers: {} };
    response = { end: sinon.spy(), writeHead: sinon.spy() };
  });

  afterEach(() => {
    readRequestStub.restore();
    requestCallStub.restore();
    miscLoggerStub.restore();
  });

  it('should handle request', async () => {
    const req_data = JSON.stringify({ method: 'GET', path: 'test' });
    readRequestStub.returns(Promise.resolve(req_data));
    requestCallStub.returns(Promise.resolve({ data: 'test', statusCode: 200, headers: {} }));

    await hub.handleXDRPC(request, response);
  });
});

describe('updateCdpCapabilities', () => {
  const REMOTE_DEBUGGER_PORT = 9222;
  const wsProxyHost = 'wsProxyHost';
  let sandbox;
  let nestedKeyValue;
  let validateCdpSeleniumJar;

  let options;
  let k;

  beforeEach(() => {
    options = {
      browserstackParams: {
        'browserstack.selenium.jar.version': '4.20.0',
        'browserstack.seleniumCdp': 'true',
      },
      port: '4444',
      host_name: '1234',
      rproxyHost: 'new_host',
      sessionId: 'session_id',
    };

    k = {
      value: {
        capabilities: {
          'se:cdp': 'http://1234:4444/se/cdp',
        },
      },
    };

    sandbox = sinon.sandbox.create();
    nestedKeyValue = sandbox.stub(helperFn, 'nestedKeyValue');
    validateCdpSeleniumJar = sandbox.stub(helperFn, 'validateCdpSeleniumJar');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should update se:cdp if conditions are met', () => {
    nestedKeyValue.returns(true);
    validateCdpSeleniumJar.returns(true);

    const result = hub.updateCdpCapabilities(options, k, wsProxyHost);

    expect(result.options.seCdpUpstream).to.equal(`http://new_host:${REMOTE_DEBUGGER_PORT}/se/cdp`);
    expect(result.k.value.capabilities['se:cdp']).to.equal(`${constants.WS_PROXY_SCHEME}://${wsProxyHost}:${constants.WS_PROXY_PORT}/session/session_id/se/cdp`);
  });

  it('should delete se:cdp if conditions are not met', () => {
    nestedKeyValue.returns(true);
    validateCdpSeleniumJar.returns(false);

    const result = hub.updateCdpCapabilities(options, k, wsProxyHost);

    expect(result.k.value.capabilities['se:cdp']).to.be.undefined;
  });
});

describe('updateBidiCapabilities', () => {
  const REMOTE_DEBUGGER_PORT = 9222;
  const wsProxyHost = 'wsProxyHost';
  let sandbox;
  let nestedKeyValue;
  let validateBidiSeleniumJar;

  let options;
  let k;

  beforeEach(() => {
    options = {
      browserstackParams: {
        'browserstack.selenium.jar.version': '4.20.0',
        'browserstack.seleniumBidi': 'true',
      },
      port: '4444',
      host_name: '1234',
      rproxyHost: 'new_host',
      sessionId: 'session_id',
    };

    k = {
      value: {
        capabilities: {
          webSocketUrl: 'http://1234:4444/se/bidi',
        },
      },
    };

    sandbox = sinon.sandbox.create();
    nestedKeyValue = sandbox.stub(helperFn, 'nestedKeyValue');
    validateBidiSeleniumJar = sandbox.stub(helperFn, 'validateBidiSeleniumJar');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should update webSocketUrl and se:gridWebSocketUrl if conditions are met', () => {
    nestedKeyValue.returns(true);
    validateBidiSeleniumJar.returns(true);

    const result = hub.updateBidiCapabilities(options, k, wsProxyHost);

    expect(result.options.seBidiUpstream).to.equal(`http://new_host:${REMOTE_DEBUGGER_PORT}/se/bidi`);
    expect(result.k.value.capabilities.webSocketUrl).to.equal(`${constants.WS_PROXY_SCHEME}://${wsProxyHost}:${constants.WS_PROXY_PORT}/session/session_id/se/bidi`);
    expect(result.k.value.capabilities['se:gridWebSocketUrl']).to.equal(`${constants.WS_PROXY_SCHEME}://${wsProxyHost}:${constants.WS_PROXY_PORT}/session/session_id`);
  });

  it('should delete webSocketUrl and se:gridWebSocketUrl if conditions are not met', () => {
    nestedKeyValue.returns(true);
    validateBidiSeleniumJar.returns(false);

    const result = hub.updateBidiCapabilities(options, k, wsProxyHost);

    expect(result.k.value.capabilities.webSocketUrl).to.be.undefined;
    expect(result.k.value.capabilities['se:gridWebSocketUrl']).to.be.undefined;
  });

  describe('WebSocket upgrade headers', () => {
    let req;
    let socket;
    let head;
    let checkSocketUpgradeStub;
    let wsHandlerStub;

    beforeEach(() => {
      req = {
        headers: {},
        url: '/wd/hub/status',
      };
      socket = {
        write: sinon.stub(),
        destroy: sinon.stub(),
      };
      head = {};

      // Create stub for WebSocketHandler.checkSocketUpgrade
      checkSocketUpgradeStub = sinon.stub(WebSocketHandler, 'checkSocketUpgrade');
      const wsHandler = new WebSocketHandler({ noServer: true });
      // Create stub for wsHandler.handleUpgrade
      wsHandlerStub = sinon.stub(wsHandler, 'handleUpgrade');
    });

    afterEach(() => {
      // Restore all stubs
      checkSocketUpgradeStub.restore();
      wsHandlerStub.restore();
    });

    it('should calculate nginxToHubTime if x-nginx-out-time header is present', () => {
      // Setup stub to execute the callback with success
      checkSocketUpgradeStub.yields(null, { some: 'data' });

      req.headers['x-nginx-out-time'] = (Date.now() - 100).toString();
      hub.server.emit('upgrade', req, socket, head);

      assert.isNumber(req.nginxToHubTime);
      assert.isAbove(req.nginxToHubTime, 0);
    });

    it('should not set nginxToHubTime if x-nginx-out-time header is missing', () => {
      // Setup stub to execute the callback with success
      checkSocketUpgradeStub.yields(null, { some: 'data' });

      hub.server.emit('upgrade', req, socket, head);

      assert.isUndefined(req.nginxToHubTime);
    });

    it('should parse userToNginx if x-rtt header is present', () => {
      // Setup stub to execute the callback with success
      checkSocketUpgradeStub.yields(null, { some: 'data' });

      req.headers['x-rtt'] = '150';
      hub.server.emit('upgrade', req, socket, head);

      assert.equal(req.userToNginx, 150);
    });

    it('should not set userToNginx if x-rtt header is missing', () => {
      // Setup stub to execute the callback with success
      checkSocketUpgradeStub.yields(null, { some: 'data' });

      hub.server.emit('upgrade', req, socket, head);

      assert.isUndefined(req.userToNginx);
    });
  });
});
